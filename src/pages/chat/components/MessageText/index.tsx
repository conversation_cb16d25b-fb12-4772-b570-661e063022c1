import { Message } from '@/store/chat'
import { memo, useMemo } from 'react'

export const MessageText = memo(({ message }: { message: Message }) => {
  // 将字符串中的 \n 转换为真实的换行符
  const data = useMemo(() => {
    let type = 'text'
    let content = message.content
    let imageUrl: string | string[] = ''
    let textContent = ''

    // 如果内容中包含 \n 字符序列，将其替换为真实的换行符
    if (content && content.includes('\\n')) {
      content = content.replace(/\\n/g, '\n')
    }

    if (content.includes('img:')) {
      type = 'image'
      // 处理 'img:xxx,xxx,xxx;text:xxx' 格式
      if (content.includes(';text:')) {
        const parts = content.split(';text:')
        const imgPart = parts[0].replace('img:', '')
        // 支持多个图片URL，用逗号分隔
        imageUrl = imgPart.includes(',') ? imgPart.split(',') : [imgPart]
        textContent = parts[1]
      } else {
        // 处理只有 'img:xxx' 或 'img:xxx,xxx,xxx' 的情况
        const imgPart = content.replace('img:', '')
        imageUrl = imgPart.includes(',') ? imgPart.split(',') : [imgPart]
      }
    }

    const isHuman = message.type === 'human'
    const containsImage = /!\[.*?\]\(.*?\)/.test(message.content) // 检测 Markdown 是否包含图片
    return {
      processedContent: content,
      isHuman,
      containsImage,
      type,
      imageUrl,
      textContent
    }
  }, [message])

  return data.processedContent ? (
    <div className={`flex rounded-[20px] ${data.isHuman ? 'justify-end mx-[20px]' : 'justify-start'} mb-2 AA`}>
      {data.type === 'image' && (
        <div className="flex flex-col">
          {/* 如果有文本内容，显示在图片上方 */}
          {data.textContent && (
            <div
              className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto mb-2 ${
                data.isHuman ? 'bg-[#000000] text-white' : 'text-black'
              }`}
            >
              {data.textContent}
            </div>
          )}
          {/* 图片显示 */}
          <div className="flex flex-col gap-2">
            {Array.isArray(data.imageUrl) ? (
              data.imageUrl.map((url, index) => (
                <div key={index} className="bg-[#EFEFF2] w-[500px] h-[500px] rounded-[16px]">
                  <img className="w-[500px] h-[500px]" src={url} alt="" />
                </div>
              ))
            ) : (
              <div className="bg-[#EFEFF2] w-[500px] h-[500px] rounded-[16px]">
                <img className="w-[500px] h-[500px]" src={data.imageUrl} alt="" />
              </div>
            )}
          </div>
        </div>
      )}
      {data.type === 'text' && (
        <div
          className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto ${
            data.isHuman ? 'bg-[#000000] text-white' : 'text-black'
          } ${data.isHuman && data.containsImage ? 'flex flex-col items-end' : ''}`} // 添加样式
        >
          {data.processedContent}
        </div>
      )}
    </div>
  ) : null
})
