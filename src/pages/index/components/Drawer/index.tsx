import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { Image } from '@tarojs/components'
import { drawerState } from '@/store'
import demoImg from '@/assets/images/inspiration/demo.png'
import addImg from '@/assets/images/index/add.png'
import deleteImg from '@/assets/images/index/delete.png'
import Taro, { useDidShow } from '@tarojs/taro'
import { useAsyncFn } from 'react-use'
import { userinfoState } from '@/store/global'
import { useAtomValue } from 'jotai'
import { useEffect } from 'react'
import { currentThreadIdState } from '@/store/chat'

interface Message {
  id: string
  name: string | null
  type: string
  content: string
  example: boolean
  additional_kwargs: Record<string, unknown>
  response_metadata: Record<string, unknown>
  tool_calls?: any[] // 更具体的类型可以根据实际需求定义
  usage_metadata?: null
  invalid_tool_calls?: any[] // 更具体的类型可以根据实际需求定义
}

interface Configurable {
  'user-agent': string
  'x-request-id': string
  'x-forward-for': string
  __after_seconds__: number
  'x-forwarded-proto': string
  langgraph_auth_user: null
  langgraph_request_id: string
  langgraph_auth_user_id: string
  __request_start_time_ms__: number
  langgraph_auth_permissions: any[] // 更具体的类型可以根据实际需求定义
}

interface Config {
  configurable: Configurable
}

interface Metadata {
  id: number
  graph_id: string
  assistant_id: string
}

interface Thread {
  thread_id: string
  created_at: string
  updated_at: string
  metadata: Metadata
  status: string
  config: Config
  values: {
    messages: Message[]
  }
  interrupts: Record<string, unknown>
  error: null
}

const Drawer = () => {
  const drawer = useObjAtom(drawerState)
  const userinfo = useAtomValue(userinfoState)
  const currentThreadId = useObjAtom(currentThreadIdState)

  const [deleteThreadState, deleteThreadFetch] = useAsyncFn(async (thread_id: string) => {
    const res = await Taro.request({
      url: `${process.env.TARO_APP_API_AI}/agent/threads/${thread_id}`,
      method: 'DELETE'
    })

    searchThreadFetch()

    console.log('res', res)
    return res
  }, [])

  const [searchThreadState, searchThreadFetch] = useAsyncFn(async () => {
    const res = await Taro.request({
      url: `${process.env.TARO_APP_API_AI}/agent/threads/search`,
      method: 'POST',
      data: { metadata: { userId: userinfo?.userId || 1 }, limit: 1000 }
    })
    const list = res.data as Thread[]
    const isExist = list.find((item) => item.thread_id === currentThreadId.val)
    if (!isExist) {
      currentThreadId.set(list[0]?.thread_id || null)
      Taro.setStorageSync('threadId', list[0]?.thread_id || null)
    }
    return list
  }, [])

  useEffect(() => {
    searchThreadFetch()
  }, [])

  useDidShow(() => {
    searchThreadFetch()
  })

  return (
    <>
      <div
        onClick={() => {
          drawer.set(false)
        }}
        className="fixed z-[1000] left-0 h-screen w-screen top-0 bg-[#0000007F]"
        style={{
          left: drawer.val ? '0' : '-100%'
        }}
      ></div>
      <div
        className="fixed z-[1001] left-0 h-screen w-[560px] top-0 bg-white pt-[74px] px-[24px] flex flex-col pb-[154px] box-border"
        style={{
          left: drawer.val ? '0' : '-560px',
          transition: 'left 0.2s ease-in-out'
        }}
      >
        <div className="flex items-center h-[76px] mb-[40px]">
          <Image mode="aspectFill" className="w-[76px] h-[76px] rounded-full mr-[14px]" src={userinfo?.head || demoImg} />
          <div className="font-medium text-[24px] text-[#202020] leading-[44px] text-left not-italic">
            {userinfo?.nickName || userinfo?.userId || '未知用户'}
          </div>
        </div>

        <div
          onClick={() => {
            Taro.showLoading({ title: 'loading...', mask: true })
            Taro.request({
              url: `${process.env.TARO_APP_API_AI}/agent/threads`,
              method: 'POST',
              data: { metadata: { userId: userinfo?.userId || 1 } }
            }).then((res) => {
              console.log('新建对话成功:', res)
              Taro.hideLoading()
              const threadId = res.data.thread_id
              currentThreadId.set(threadId)
              Taro.setStorageSync('threadId', threadId)
              Taro.navigateTo({
                url: '/pages/chat/index',
                success: () => {
                  drawer.set(false)
                }
              })
            })
          }}
          className="w-[510px] h-[80px] rounded-[16px] border-2 border-solid border-[#303335] flex items-center mb-[32px]"
        >
          <img className="w-[40px] h-[40px] ml-[20px] mr-[16px]" src={addImg} alt="" />
          <div className="font-medium text-[28px] text-black leading-[28px] text-center not-italic">新对话</div>
        </div>

        <div className="w-[510px] h-[2px] opacity-10 bg-black mb-[32px]"></div>

        <div className="flex-1 h-0 flex flex-col">
          <div className="font-normal text-[24px] text-[#202020] leading-[44px] text-left not-italic mb-[12px]">历史</div>

          <div className="overflow-y-auto overflow-x-hidden flex-1 h-0 no-scrollbar">
            {searchThreadState.value?.map((thread) => {
              // 处理 'img:xxx,xxx,xxx;text:xxx' 格式
              let content = thread.values?.messages[0].content || ''
              let title = ''
              let img = ''

              if (content.includes('img:')) {
                try {
                  // 分离图片和文本部分
                  const parts = content.split(';')

                  // 解析图片部分，获取第一张图片
                  const imgPart = parts.find((part) => part.startsWith('img:'))
                  if (imgPart) {
                    const imgUrls = imgPart.replace('img:', '').split(',')
                    img = imgUrls[0] || '' // 取第一张图片
                  }

                  // 解析文本部分
                  const textPart = parts.find((part) => part.startsWith('text:'))
                  if (textPart) {
                    title = textPart.replace('text:', '')
                  } else {
                    title = '[图片]' // 如果没有文本部分，显示默认文本
                  }
                } catch (error) {
                  title = '[图片]'
                  img = ''
                }
              } else {
                // 如果没有图片，但有text:前缀，则去除前缀
                if (content.startsWith('text:')) {
                  title = content.replace('text:', '')
                } else {
                  title = content
                }
              }
              return (
                <div key={thread.thread_id} className="w-[510px] h-[108px] rounded-[16px] flex items-center">
                  <div className="w-[80px] h-[80px] rounded-[12px] overflow-hidden bg-[#f1f1f1] mr-[12px]">
                    <img
                      onClick={() => {
                        currentThreadId.set(thread.thread_id)
                        Taro.setStorageSync('threadId', thread.thread_id)
                        Taro.navigateTo({
                          url: '/pages/chat/index',
                          success: () => {
                            drawer.set(false)
                          }
                        })
                      }}
                      className="w-[80px] h-[80px] rounded-[12px]"
                      src={img || demoImg}
                      alt=""
                    />
                  </div>
                  <div
                    onClick={() => {
                      currentThreadId.set(thread.thread_id)
                      Taro.setStorageSync('threadId', thread.thread_id)
                      Taro.navigateTo({
                        url: '/pages/chat/index',
                        success: () => {
                          drawer.set(false)
                        }
                      })
                    }}
                    className="flex-1 h-full flex items-center font-normal text-[28px] text-black leading-[28px] text-left not-italic"
                  >
                    <div className="c-line-clamp-1">{title || '暂无对话内容'}</div>
                  </div>
                  <div
                    onClick={() => {
                      Taro.showModal({
                        title: '删除对话',
                        content: '确定删除此对话吗？',
                        confirmColor: '#FF3B30',
                        success: (res) => {
                          if (res.confirm) {
                            deleteThreadFetch(thread.thread_id)
                          }
                        }
                      })
                    }}
                    className="flex_center w-[80px] h-[80px] mr-[-20px]"
                  >
                    <img className="w-[28px] h-[28px]" src={deleteImg} alt="" />
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </>
  )
}

export default Drawer
